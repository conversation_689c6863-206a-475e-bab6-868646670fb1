# Tactical RMM Network Scanner
# Script para scan de rede via Tactical RMM
# Autor: <PERSON>

param(
    [Parameter(Mandatory=$true)]
    [string]$NetworkRange,
    
    [Parameter(Mandatory=$false)]
    [int]$Threads = 50,
    
    [Parameter(Mandatory=$false)]
    [int]$Timeout = 2,
    
    [Parameter(Mandatory=$false)]
    [switch]$JsonOutput
)

# Função para validar formato de rede
function Test-NetworkFormat {
    param([string]$Network)
    
    if ($Network -match '^(\d{1,3}\.){3}\d{1,3}/\d{1,2}$') {
        return $true
    }
    return $false
}

# Função para baixar e executar o scanner Python
function Invoke-NetworkScan {
    param(
        [string]$Network,
        [int]$Threads,
        [int]$Timeout,
        [bool]$Json
    )
    
    try {
        # Verificar se Python está instalado
        $pythonCmd = $null
        foreach ($cmd in @('python3', 'python', 'py')) {
            try {
                $version = & $cmd --version 2>$null
                if ($version -match "Python 3") {
                    $pythonCmd = $cmd
                    break
                }
            }
            catch {
                continue
            }
        }
        
        if (-not $pythonCmd) {
            Write-Output "TACTICAL-ERROR: Python 3 não encontrado. Instale Python 3.6+ primeiro."
            return
        }
        
        Write-Output "TACTICAL-INFO: Usando $pythonCmd"
        
        # Criar script Python temporário
        $scriptContent = @'
#!/usr/bin/env python3
import subprocess
import threading
import ipaddress
import socket
import sys
import time
import json
import argparse
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

class TacticalNetworkScanner:
    def __init__(self, network, max_workers=50, timeout=2):
        self.network = ipaddress.IPv4Network(network)
        self.max_workers = max_workers
        self.timeout = timeout
        self.results = []
        self.lock = threading.Lock()
        
        self.common_ports = {22: "SSH", 23: "Telnet", 80: "HTTP", 135: "RPC", 139: "NetBIOS", 443: "HTTPS", 445: "SMB", 3389: "RDP", 5353: "mDNS", 8080: "HTTP-Alt", 9100: "Printer", 62078: "iPhone-Sync"}
        self.os_signatures = {64: "Linux/Unix", 128: "Windows", 255: "Network Device", 60: "macOS/iOS"}
        self.known_models = {'archer-c5': 'TP-Link Archer C5', 'archer-c7': 'TP-Link Archer C7', 'dir-615': 'D-Link DIR-615', 'r7000': 'Netgear R7000', 'rt-ac68u': 'ASUS RT-AC68U'}

    def ping_host(self, ip):
        try:
            if sys.platform.startswith('win'):
                cmd = ['ping', '-n', '1', '-w', str(self.timeout * 1000), str(ip)]
            else:
                cmd = ['ping', '-c', '1', '-W', str(self.timeout), str(ip)]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.timeout + 1)
            if result.returncode == 0:
                ttl = self.extract_ttl(result.stdout)
                return True, ttl
            return False, None
        except:
            return False, None

    def extract_ttl(self, ping_output):
        try:
            if sys.platform.startswith('win'):
                for line in ping_output.split('\n'):
                    if 'TTL=' in line:
                        return int(line.split('TTL=')[1].split()[0])
            else:
                for line in ping_output.split('\n'):
                    if 'ttl=' in line:
                        return int(line.split('ttl=')[1].split()[0])
        except:
            pass
        return None

    def get_hostname(self, ip):
        try:
            return socket.gethostbyaddr(str(ip))[0]
        except:
            return None

    def scan_ports(self, ip):
        ports = [22, 23, 80, 135, 139, 443, 445, 3389, 5353, 8080, 9100, 62078]
        open_ports = []
        for port in ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(0.5)
                if sock.connect_ex((str(ip), port)) == 0:
                    open_ports.append((port, self.common_ports.get(port, f"Port-{port}")))
                sock.close()
            except:
                pass
        return open_ports

    def identify_device(self, ttl, open_ports, hostname):
        os_info = "Unknown"
        device_type = "Unknown Device"
        method = "Unknown"
        
        # TTL identification
        if ttl:
            for ttl_sig, os_name in self.os_signatures.items():
                if abs(ttl - ttl_sig) <= 5:
                    os_info = os_name
                    method = f"TTL={ttl}"
                    break
        
        # Port-based identification
        port_numbers = [port for port, _ in open_ports]
        if 3389 in port_numbers:
            device_type = "Windows Server/Desktop"
            os_info = "Windows"
        elif 22 in port_numbers:
            device_type = "Linux Server"
            os_info = "Linux"
        elif 9100 in port_numbers:
            device_type = "Network Printer"
        elif 62078 in port_numbers:
            device_type = "iPhone/iPad"
            os_info = "iOS"
        
        # Hostname identification
        if hostname:
            hostname_lower = hostname.lower()
            for model_key, model_name in self.known_models.items():
                if model_key in hostname_lower:
                    device_type = model_name
                    method += "+Model"
                    break
            
            if 'router' in hostname_lower or 'tp-link' in hostname_lower:
                device_type = "Router"
            elif 'printer' in hostname_lower:
                device_type = "Printer"
        
        return os_info, device_type, method

    def scan_host(self, ip):
        try:
            is_alive, ttl = self.ping_host(ip)
            if not is_alive:
                return None
            
            hostname = self.get_hostname(ip)
            open_ports = self.scan_ports(ip)
            os_info, device_type, method = self.identify_device(ttl, open_ports, hostname)
            
            result = {
                'ip': str(ip), 'hostname': hostname or 'N/A', 'ttl': ttl,
                'os': os_info, 'device_type': device_type, 'method': method,
                'open_ports': open_ports, 'scan_time': datetime.now().isoformat()
            }
            
            with self.lock:
                self.results.append(result)
                print(f"FOUND: {str(ip)} | {hostname or 'N/A'} | {os_info} | {device_type}")
            
            return result
        except:
            return None

    def scan_network(self):
        print(f"TACTICAL-SCAN-START: {self.network} | {self.network.num_addresses} IPs")
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {executor.submit(self.scan_host, ip): ip for ip in self.network.hosts()}
            for future in as_completed(futures):
                try:
                    future.result()
                except:
                    pass
        
        end_time = time.time()
        print(f"TACTICAL-SCAN-COMPLETE: {len(self.results)} devices found in {end_time - start_time:.1f}s")
        return self.results

if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("Usage: script.py <network> [threads] [timeout] [json]")
        sys.exit(1)
    
    network = sys.argv[1]
    threads = int(sys.argv[2]) if len(sys.argv) > 2 else 50
    timeout = int(sys.argv[3]) if len(sys.argv) > 3 else 2
    json_output = len(sys.argv) > 4 and sys.argv[4].lower() == 'true'
    
    scanner = TacticalNetworkScanner(network, threads, timeout)
    results = scanner.scan_network()
    
    if json_output:
        output = {'scan_info': {'network': network, 'total_hosts': len(results)}, 'results': results}
        print("TACTICAL-JSON:", json.dumps(output))
'@
        
        # Salvar script temporário
        $tempScript = [System.IO.Path]::GetTempFileName() + ".py"
        $scriptContent | Out-File -FilePath $tempScript -Encoding UTF8
        
        # Executar scanner
        $args = @($tempScript, $Network, $Threads, $Timeout)
        if ($Json) {
            $args += "true"
        }
        
        Write-Output "TACTICAL-INFO: Iniciando scan da rede $Network com $Threads threads"
        
        $output = & $pythonCmd @args 2>&1
        
        # Processar output
        foreach ($line in $output) {
            Write-Output $line
        }
        
        # Limpar arquivo temporário
        Remove-Item $tempScript -Force -ErrorAction SilentlyContinue
        
    }
    catch {
        Write-Output "TACTICAL-ERROR: $($_.Exception.Message)"
    }
}

# Validar entrada
if (-not (Test-NetworkFormat $NetworkRange)) {
    Write-Output "TACTICAL-ERROR: Formato de rede inválido. Use formato CIDR (ex: ***********/24)"
    exit 1
}

# Executar scan
Write-Output "TACTICAL-INFO: Network Scanner v1.0 - Paulo Matheus"
Write-Output "TACTICAL-INFO: Rede: $NetworkRange | Threads: $Threads | Timeout: $Timeout"

Invoke-NetworkScan -Network $NetworkRange -Threads $Threads -Timeout $Timeout -Json $JsonOutput.IsPresent

Write-Output "TACTICAL-INFO: Scan concluído"
