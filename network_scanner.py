#!/usr/bin/env python3

"""
Network Scanner - Scan de rede ***********/16
Identifica dispositivos e sistemas operacionais na rede
Autor: Paulo Matheus
Data: 2025-01-08
"""

import subprocess
import threading
import ipaddress
import socket
import sys
import time
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse

class NetworkScanner:
    def __init__(self, network="***********/16", max_workers=100, timeout=2):
        self.network = ipaddress.IPv4Network(network)
        self.max_workers = max_workers
        self.timeout = timeout
        self.results = []
        self.lock = threading.Lock()
        
        # Portas comuns para identificação de serviços
        self.common_ports = {
            22: "SSH",
            23: "Telnet", 
            25: "SMTP",
            53: "DNS",
            80: "HTTP",
            110: "POP3",
            135: "RPC",
            139: "NetBIOS",
            143: "IMAP",
            443: "HTTPS",
            445: "SMB",
            993: "IMAPS",
            995: "POP3S",
            3389: "RDP",
            5900: "VNC",
            8080: "HTTP-Alt"
        }
        
        # Assinaturas de OS baseadas em TTL e outras características
        self.os_signatures = {
            64: "Linux/Unix",
            128: "Windows",
            255: "Cisco/Network Device",
            32: "Windows 95/98",
            60: "macOS"
        }

    def print_banner(self):
        """Exibe banner do scanner"""
        print("=" * 70)
        print("🌐 NETWORK SCANNER - Identificação de Dispositivos")
        print("=" * 70)
        print(f"📡 Rede: {self.network}")
        print(f"🔍 IPs a escanear: {self.network.num_addresses}")
        print(f"⚡ Threads: {self.max_workers}")
        print(f"⏱️  Timeout: {self.timeout}s")
        print(f"🕐 Início: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)

    def ping_host(self, ip):
        """Verifica se o host está ativo via ping"""
        try:
            # Usar ping do sistema operacional
            if sys.platform.startswith('win'):
                cmd = ['ping', '-n', '1', '-w', str(self.timeout * 1000), str(ip)]
            else:
                cmd = ['ping', '-c', '1', '-W', str(self.timeout), str(ip)]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.timeout + 1)
            
            if result.returncode == 0:
                # Extrair TTL do ping para identificação de OS
                ttl = self.extract_ttl(result.stdout)
                return True, ttl
            return False, None
            
        except (subprocess.TimeoutExpired, Exception):
            return False, None

    def extract_ttl(self, ping_output):
        """Extrai TTL do output do ping"""
        try:
            if sys.platform.startswith('win'):
                # Windows: TTL=64
                for line in ping_output.split('\n'):
                    if 'TTL=' in line:
                        ttl = line.split('TTL=')[1].split()[0]
                        return int(ttl)
            else:
                # Linux: ttl=64
                for line in ping_output.split('\n'):
                    if 'ttl=' in line:
                        ttl = line.split('ttl=')[1].split()[0]
                        return int(ttl)
        except:
            pass
        return None

    def get_hostname(self, ip):
        """Tenta resolver o hostname do IP"""
        try:
            hostname = socket.gethostbyaddr(str(ip))[0]
            return hostname
        except:
            return None

    def scan_ports(self, ip, ports=None):
        """Escaneia portas específicas do host"""
        if ports is None:
            ports = list(self.common_ports.keys())
        
        open_ports = []
        for port in ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(0.5)
                result = sock.connect_ex((str(ip), port))
                if result == 0:
                    service = self.common_ports.get(port, f"Port-{port}")
                    open_ports.append((port, service))
                sock.close()
            except:
                pass
        
        return open_ports

    def identify_os(self, ttl, open_ports, hostname):
        """Identifica o sistema operacional baseado em várias características"""
        os_info = "Desconhecido"
        confidence = 0
        
        # Identificação por TTL
        if ttl:
            for ttl_sig, os_name in self.os_signatures.items():
                if abs(ttl - ttl_sig) <= 5:  # Tolerância de ±5
                    os_info = os_name
                    confidence = 70
                    break
        
        # Refinamento baseado em portas abertas
        port_numbers = [port for port, _ in open_ports]
        
        if 445 in port_numbers or 135 in port_numbers or 3389 in port_numbers:
            if "Windows" not in os_info:
                os_info = "Windows"
                confidence = 80
            else:
                confidence = 90
                
        if 22 in port_numbers and 80 in port_numbers:
            if ttl and 60 <= ttl <= 68:
                os_info = "Linux"
                confidence = 85
                
        if 22 in port_numbers and ttl and ttl == 255:
            os_info = "Network Device (Router/Switch)"
            confidence = 90
            
        # Refinamento por hostname
        if hostname:
            hostname_lower = hostname.lower()
            if any(x in hostname_lower for x in ['windows', 'win', 'pc', 'desktop']):
                os_info = "Windows"
                confidence = max(confidence, 75)
            elif any(x in hostname_lower for x in ['linux', 'ubuntu', 'debian', 'centos', 'server']):
                os_info = "Linux"
                confidence = max(confidence, 75)
            elif any(x in hostname_lower for x in ['router', 'switch', 'cisco', 'tp-link']):
                os_info = "Network Device"
                confidence = max(confidence, 80)
        
        return os_info, confidence

    def get_device_type(self, open_ports, hostname, os_info):
        """Determina o tipo de dispositivo"""
        port_numbers = [port for port, _ in open_ports]
        
        if 3389 in port_numbers:
            return "Windows Server/Desktop"
        elif 22 in port_numbers and 80 in port_numbers:
            return "Linux Server"
        elif 22 in port_numbers:
            return "Linux/Unix System"
        elif 80 in port_numbers or 443 in port_numbers:
            return "Web Server"
        elif any(p in port_numbers for p in [23, 161, 162]):
            return "Network Device"
        elif hostname and any(x in hostname.lower() for x in ['printer', 'print']):
            return "Printer"
        elif len(port_numbers) == 0:
            return "Unknown Device"
        else:
            return "Generic Device"

    def scan_host(self, ip):
        """Escaneia um host específico"""
        try:
            # Ping test
            is_alive, ttl = self.ping_host(ip)
            
            if not is_alive:
                return None
            
            # Resolver hostname
            hostname = self.get_hostname(ip)
            
            # Scan de portas
            open_ports = self.scan_ports(ip)
            
            # Identificar OS
            os_info, confidence = self.identify_os(ttl, open_ports, hostname)
            
            # Determinar tipo de dispositivo
            device_type = self.get_device_type(open_ports, hostname, os_info)
            
            result = {
                'ip': str(ip),
                'hostname': hostname or 'N/A',
                'ttl': ttl,
                'os': os_info,
                'confidence': confidence,
                'device_type': device_type,
                'open_ports': open_ports,
                'scan_time': datetime.now().isoformat()
            }
            
            with self.lock:
                self.results.append(result)
                print(f"✅ {str(ip):<15} | {hostname or 'N/A':<20} | {os_info:<20} | {device_type}")

            return result
            
        except Exception as e:
            print(f"❌ Erro ao escanear {str(ip)}: {e}")
            return None

    def scan_network(self):
        """Escaneia toda a rede"""
        self.print_banner()
        
        print(f"{'IP':<15} | {'Hostname':<20} | {'OS':<20} | {'Tipo'}")
        print("-" * 80)
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submeter todas as tarefas
            futures = {executor.submit(self.scan_host, ip): ip for ip in self.network.hosts()}
            
            # Processar resultados conforme completam
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    ip = futures[future]
                    print(f"❌ Erro ao processar {str(ip)}: {e}")
        
        end_time = time.time()
        
        # Resumo final
        self.print_summary(end_time - start_time)

    def print_summary(self, scan_time):
        """Exibe resumo do scan"""
        print("\n" + "=" * 70)
        print("📊 RESUMO DO SCAN")
        print("=" * 70)
        print(f"⏱️  Tempo total: {scan_time:.2f} segundos")
        print(f"🎯 Hosts encontrados: {len(self.results)}")
        print(f"📡 Total de IPs escaneados: {self.network.num_addresses}")
        
        # Estatísticas por OS
        os_stats = {}
        device_stats = {}
        
        for result in self.results:
            os_name = result['os']
            device_type = result['device_type']
            
            os_stats[os_name] = os_stats.get(os_name, 0) + 1
            device_stats[device_type] = device_stats.get(device_type, 0) + 1
        
        print("\n🖥️  Sistemas Operacionais encontrados:")
        for os_name, count in sorted(os_stats.items()):
            print(f"   {os_name}: {count}")
        
        print("\n📱 Tipos de dispositivos encontrados:")
        for device_type, count in sorted(device_stats.items()):
            print(f"   {device_type}: {count}")

    def save_results(self, filename=None):
        """Salva resultados em arquivo JSON"""
        if filename is None:
            filename = f"network_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'scan_info': {
                    'network': str(self.network),
                    'scan_time': datetime.now().isoformat(),
                    'total_hosts': len(self.results)
                },
                'results': self.results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Resultados salvos em: {filename}")

def main():
    parser = argparse.ArgumentParser(description='Network Scanner - Identifica dispositivos na rede')
    parser.add_argument('--network', '-n', default='***********/16', 
                       help='Rede para escanear (padrão: ***********/16)')
    parser.add_argument('--threads', '-t', type=int, default=100,
                       help='Número de threads (padrão: 100)')
    parser.add_argument('--timeout', type=int, default=2,
                       help='Timeout em segundos (padrão: 2)')
    parser.add_argument('--save', '-s', action='store_true',
                       help='Salvar resultados em arquivo JSON')
    
    args = parser.parse_args()
    
    try:
        scanner = NetworkScanner(
            network=args.network,
            max_workers=args.threads,
            timeout=args.timeout
        )
        
        scanner.scan_network()
        
        if args.save:
            scanner.save_results()
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Scan interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro durante o scan: {e}")

if __name__ == "__main__":
    main()
