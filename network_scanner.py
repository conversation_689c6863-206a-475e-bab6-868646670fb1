#!/usr/bin/env python3

"""
Network Scanner - Scan de rede ***********/16
Identifica dispositivos e sistemas operacionais na rede
Autor: Paulo Matheus
Data: 2025-01-08
"""

import subprocess
import threading
import ipaddress
import socket
import sys
import time
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse

class NetworkScanner:
    def __init__(self, network="***********/16", max_workers=100, timeout=2):
        self.network = ipaddress.IPv4Network(network)
        self.max_workers = max_workers
        self.timeout = timeout
        self.results = []
        self.lock = threading.Lock()
        
        # Portas comuns para identificação de serviços
        self.common_ports = {
            22: "SSH",
            23: "Telnet",
            25: "SMTP",
            53: "DNS",
            80: "HTTP",
            110: "POP3",
            135: "RPC",
            139: "NetBIOS",
            143: "IMAP",
            443: "HTTPS",
            445: "SMB",
            993: "IMAPS",
            995: "POP3S",
            1900: "UPnP",
            3389: "RDP",
            5000: "UPnP",
            5353: "mDNS",
            5900: "VNC",
            8080: "HTTP-Alt",
            8443: "HTTPS-Alt",
            9100: "Printer",
            10000: "Webmin",
            62078: "iPhone-Sync"
        }

        # Portas específicas para dispositivos
        self.device_ports = {
            # Roteadores e Network Devices
            23: ["Router", "Switch", "Firewall"],
            80: ["Router", "Access Point", "Firewall", "Server"],
            443: ["Router", "Access Point", "Firewall", "Server"],
            161: ["Router", "Switch", "Firewall", "Printer"],
            162: ["Router", "Switch", "Firewall"],
            8080: ["Router", "Access Point"],
            8443: ["Router", "Access Point"],

            # Dispositivos móveis
            62078: ["iPhone", "iPad"],
            5353: ["Apple Device", "Android"],
            1900: ["Android", "Smart TV"],

            # Servidores e serviços
            22: ["Linux Server", "Unix Server"],
            3389: ["Windows Server"],
            5900: ["VNC Server"],
            9100: ["Printer"],
            10000: ["Linux Server"]
        }
        
        # Assinaturas de OS baseadas em TTL e outras características
        self.os_signatures = {
            64: "Linux/Unix",
            128: "Windows",
            255: "Cisco/Network Device",
            32: "Windows 95/98",
            60: "macOS/iOS"
        }

        # Padrões de hostname para identificação específica
        self.hostname_patterns = {
            # Dispositivos móveis
            'iphone': 'iPhone',
            'ipad': 'iPad',
            'android': 'Android Device',
            'samsung': 'Samsung Device',
            'xiaomi': 'Xiaomi Device',
            'huawei': 'Huawei Device',
            'oneplus': 'OnePlus Device',

            # Roteadores por marca
            'tp-link': 'TP-Link Router',
            'tplink': 'TP-Link Router',
            'dlink': 'D-Link Router',
            'd-link': 'D-Link Router',
            'linksys': 'Linksys Router',
            'netgear': 'Netgear Router',
            'asus': 'ASUS Router',
            'cisco': 'Cisco Device',
            'mikrotik': 'MikroTik Router',
            'ubiquiti': 'Ubiquiti Device',
            'unifi': 'UniFi Access Point',

            # Firewalls
            'pfsense': 'pfSense Firewall',
            'opnsense': 'OPNsense Firewall',
            'sophos': 'Sophos Firewall',
            'fortinet': 'FortiGate Firewall',
            'checkpoint': 'Check Point Firewall',

            # Impressoras
            'hp': 'HP Printer',
            'canon': 'Canon Printer',
            'epson': 'Epson Printer',
            'brother': 'Brother Printer',
            'xerox': 'Xerox Printer',
            'printer': 'Network Printer',

            # Servidores e sistemas
            'ubuntu': 'Ubuntu Server',
            'debian': 'Debian Server',
            'centos': 'CentOS Server',
            'redhat': 'Red Hat Server',
            'windows': 'Windows Machine',
            'server': 'Server',
            'nas': 'NAS Device',
            'synology': 'Synology NAS',
            'qnap': 'QNAP NAS',

            # Smart devices
            'smart-tv': 'Smart TV',
            'chromecast': 'Google Chromecast',
            'roku': 'Roku Device',
            'appletv': 'Apple TV',
            'firetv': 'Amazon Fire TV'
        }

    def print_banner(self):
        """Exibe banner do scanner"""
        print("=" * 120)
        print("🌐 NETWORK SCANNER - Identificação Avançada de Dispositivos")
        print("=" * 120)
        print(f"📡 Rede: {self.network}")
        print(f"🔍 IPs a escanear: {self.network.num_addresses}")
        print(f"⚡ Threads: {self.max_workers}")
        print(f"⏱️  Timeout: {self.timeout}s")
        print(f"🕐 Início: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔬 Métodos: TTL Analysis, Port Scanning, Hostname Analysis, HTTP Fingerprinting")
        print(f"📱 Detecta: Windows, Linux, Android, iPhone, Roteadores, Firewalls, Impressoras, etc.")
        print("=" * 120)

    def ping_host(self, ip):
        """Verifica se o host está ativo via ping"""
        try:
            # Usar ping do sistema operacional
            if sys.platform.startswith('win'):
                cmd = ['ping', '-n', '1', '-w', str(self.timeout * 1000), str(ip)]
            else:
                cmd = ['ping', '-c', '1', '-W', str(self.timeout), str(ip)]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.timeout + 1)
            
            if result.returncode == 0:
                # Extrair TTL do ping para identificação de OS
                ttl = self.extract_ttl(result.stdout)
                return True, ttl
            return False, None
            
        except (subprocess.TimeoutExpired, Exception):
            return False, None

    def extract_ttl(self, ping_output):
        """Extrai TTL do output do ping"""
        try:
            if sys.platform.startswith('win'):
                # Windows: TTL=64
                for line in ping_output.split('\n'):
                    if 'TTL=' in line:
                        ttl = line.split('TTL=')[1].split()[0]
                        return int(ttl)
            else:
                # Linux: ttl=64
                for line in ping_output.split('\n'):
                    if 'ttl=' in line:
                        ttl = line.split('ttl=')[1].split()[0]
                        return int(ttl)
        except:
            pass
        return None

    def get_hostname(self, ip):
        """Tenta resolver o hostname do IP"""
        try:
            hostname = socket.gethostbyaddr(str(ip))[0]
            return hostname
        except:
            return None

    def scan_ports(self, ip, ports=None):
        """Escaneia portas específicas do host"""
        if ports is None:
            ports = list(self.common_ports.keys())

        open_ports = []
        for port in ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(0.5)
                result = sock.connect_ex((str(ip), port))
                if result == 0:
                    service = self.common_ports.get(port, f"Port-{port}")
                    open_ports.append((port, service))
                sock.close()
            except:
                pass

        return open_ports

    def http_fingerprint(self, ip, port=80):
        """Faz fingerprinting HTTP para obter informações do servidor"""
        try:
            import urllib.request
            import urllib.error

            url = f"http://{ip}:{port}/"
            req = urllib.request.Request(url)
            req.add_header('User-Agent', 'Mozilla/5.0 (compatible; NetworkScanner/1.0)')

            response = urllib.request.urlopen(req, timeout=3)
            headers = dict(response.headers)

            server_info = {
                'server': headers.get('Server', ''),
                'title': '',
                'model': ''
            }

            # Ler conteúdo para buscar título e modelo
            content = response.read(1024).decode('utf-8', errors='ignore')

            # Extrair título
            import re
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', content, re.IGNORECASE)
            if title_match:
                server_info['title'] = title_match.group(1).strip()

            # Buscar padrões de modelo em títulos e headers
            model_patterns = {
                r'TP-LINK\s+([A-Z0-9-]+)': 'TP-Link {}',
                r'D-Link\s+([A-Z0-9-]+)': 'D-Link {}',
                r'NETGEAR\s+([A-Z0-9-]+)': 'Netgear {}',
                r'ASUS\s+([A-Z0-9-]+)': 'ASUS {}',
                r'Linksys\s+([A-Z0-9-]+)': 'Linksys {}',
                r'UniFi\s+([A-Z0-9-]+)': 'UniFi {}',
                r'MikroTik\s+([A-Z0-9-]+)': 'MikroTik {}',
                r'pfSense': 'pfSense Firewall',
                r'OPNsense': 'OPNsense Firewall'
            }

            search_text = f"{server_info['title']} {server_info['server']}"
            for pattern, model_format in model_patterns.items():
                match = re.search(pattern, search_text, re.IGNORECASE)
                if match:
                    if '{}' in model_format:
                        server_info['model'] = model_format.format(match.group(1))
                    else:
                        server_info['model'] = model_format
                    break

            return server_info

        except:
            return None

    def identify_os(self, ttl, open_ports, hostname):
        """Identifica o sistema operacional baseado em várias características"""
        os_info = "Desconhecido"
        confidence = 0
        detection_methods = []

        # Identificação por TTL
        if ttl:
            for ttl_sig, os_name in self.os_signatures.items():
                if abs(ttl - ttl_sig) <= 5:  # Tolerância de ±5
                    os_info = os_name
                    confidence = 70
                    detection_methods.append(f"TTL={ttl}")
                    break

        # Refinamento baseado em portas abertas
        port_numbers = [port for port, _ in open_ports]

        if 445 in port_numbers or 135 in port_numbers or 3389 in port_numbers:
            if "Windows" not in os_info:
                os_info = "Windows"
                confidence = 80
                detection_methods.append("Ports")
            else:
                confidence = 90
                if "Ports" not in [m.split("=")[0] for m in detection_methods]:
                    detection_methods.append("Ports")

        if 22 in port_numbers and 80 in port_numbers:
            if ttl and 60 <= ttl <= 68:
                os_info = "Linux"
                confidence = 85
                if "Ports" not in [m.split("=")[0] for m in detection_methods]:
                    detection_methods.append("Ports")

        if 22 in port_numbers and ttl and ttl == 255:
            os_info = "Network Device (Router/Switch)"
            confidence = 90
            if "Ports" not in [m.split("=")[0] for m in detection_methods]:
                detection_methods.append("Ports")

        # Refinamento por hostname
        if hostname and hostname != "N/A":
            hostname_lower = hostname.lower()
            hostname_detected = False

            if any(x in hostname_lower for x in ['windows', 'win', 'pc', 'desktop']):
                if "Windows" not in os_info:
                    os_info = "Windows"
                confidence = max(confidence, 75)
                hostname_detected = True
            elif any(x in hostname_lower for x in ['linux', 'ubuntu', 'debian', 'centos', 'server']):
                if "Linux" not in os_info:
                    os_info = "Linux"
                confidence = max(confidence, 75)
                hostname_detected = True
            elif any(x in hostname_lower for x in ['router', 'switch', 'cisco', 'tp-link']):
                os_info = "Network Device"
                confidence = max(confidence, 80)
                hostname_detected = True

            if hostname_detected:
                detection_methods.append("Hostname")

        # Se não conseguiu identificar por nenhum método
        if not detection_methods:
            detection_methods.append("Unknown")

        detection_method = "+".join(detection_methods)

        return os_info, confidence, detection_method

    def get_device_type(self, open_ports, hostname, os_info, http_info=None):
        """Determina o tipo específico de dispositivo com modelo quando possível"""
        port_numbers = [port for port, _ in open_ports]
        hostname_lower = hostname.lower() if hostname and hostname != "N/A" else ""

        device_type = "Unknown Device"
        model = ""
        confidence = 0

        # 1. Identificação por hostname (mais específica)
        for pattern, device_name in self.hostname_patterns.items():
            if pattern in hostname_lower:
                device_type = device_name
                confidence = 90
                break

        # 2. Identificação por HTTP fingerprinting
        if http_info and http_info.get('model'):
            device_type = http_info['model']
            confidence = 95
        elif http_info and http_info.get('title'):
            title_lower = http_info['title'].lower()
            for pattern, device_name in self.hostname_patterns.items():
                if pattern in title_lower:
                    device_type = device_name
                    confidence = 85
                    break

        # 3. Identificação específica por portas e padrões
        if confidence < 80:
            # Dispositivos móveis (iOS/Android)
            if 62078 in port_numbers:  # iTunes sync
                device_type = "iPhone/iPad"
                confidence = 90
            elif 5353 in port_numbers and (60 <= self.extract_ttl_from_ping(hostname) <= 64):
                if "iphone" in hostname_lower or "ipad" in hostname_lower:
                    device_type = "Apple Device (iPhone/iPad)"
                else:
                    device_type = "Apple Device"
                confidence = 80
            elif 5353 in port_numbers and 1900 in port_numbers:
                device_type = "Android Device"
                confidence = 75

            # Roteadores e Access Points
            elif (80 in port_numbers or 443 in port_numbers) and 23 in port_numbers:
                device_type = "Router/Gateway"
                confidence = 85
            elif (80 in port_numbers or 443 in port_numbers) and any(p in port_numbers for p in [161, 162]):
                device_type = "Managed Switch/Router"
                confidence = 85
            elif 8080 in port_numbers or 8443 in port_numbers:
                device_type = "Access Point/Router"
                confidence = 80

            # Firewalls
            elif 443 in port_numbers and 22 in port_numbers and any(p in port_numbers for p in [161, 4433, 10443]):
                device_type = "Firewall/Security Appliance"
                confidence = 85

            # Impressoras
            elif 9100 in port_numbers:
                device_type = "Network Printer"
                confidence = 90
            elif 80 in port_numbers and any(x in hostname_lower for x in ['printer', 'print', 'hp', 'canon', 'epson']):
                device_type = "Network Printer"
                confidence = 85

            # Servidores
            elif 3389 in port_numbers:
                if 80 in port_numbers or 443 in port_numbers:
                    device_type = "Windows Server"
                else:
                    device_type = "Windows Desktop/Server"
                confidence = 90
            elif 22 in port_numbers and len([p for p in port_numbers if p > 1000]) >= 3:
                device_type = "Linux Server"
                confidence = 85
            elif 22 in port_numbers:
                device_type = "Linux/Unix System"
                confidence = 75

            # NAS e Storage
            elif any(x in hostname_lower for x in ['nas', 'synology', 'qnap', 'storage']):
                device_type = "NAS Device"
                confidence = 90

            # Smart devices
            elif 1900 in port_numbers and 80 in port_numbers:
                device_type = "Smart Device/Media Player"
                confidence = 70

            # Web servers genéricos
            elif 80 in port_numbers or 443 in port_numbers:
                device_type = "Web Server/Service"
                confidence = 60

            # Network devices genéricos
            elif any(p in port_numbers for p in [23, 161, 162]):
                device_type = "Network Device"
                confidence = 70

        # 4. Refinamento baseado no OS identificado
        if "Windows" in os_info and "Server" not in device_type:
            if 3389 in port_numbers:
                device_type = f"{device_type} (Windows Server)" if device_type != "Unknown Device" else "Windows Server"
            else:
                device_type = f"{device_type} (Windows)" if device_type != "Unknown Device" else "Windows Machine"
        elif "Linux" in os_info and "Server" not in device_type and "Router" not in device_type:
            device_type = f"{device_type} (Linux)" if device_type != "Unknown Device" else "Linux System"
        elif "macOS" in os_info or "iOS" in os_info:
            if device_type == "Unknown Device":
                device_type = "Apple Device"

        return device_type

    def extract_ttl_from_ping(self, hostname):
        """Extrai TTL fazendo ping no hostname (método auxiliar)"""
        try:
            if hostname and hostname != "N/A":
                is_alive, ttl = self.ping_host(hostname)
                return ttl if ttl else 0
        except:
            pass
        return 0

    def scan_host(self, ip):
        """Escaneia um host específico"""
        try:
            # Ping test
            is_alive, ttl = self.ping_host(ip)
            
            if not is_alive:
                return None
            
            # Resolver hostname
            hostname = self.get_hostname(ip)
            
            # Scan de portas
            open_ports = self.scan_ports(ip)

            # HTTP fingerprinting se porta 80 estiver aberta
            http_info = None
            port_numbers = [port for port, _ in open_ports]
            if 80 in port_numbers:
                http_info = self.http_fingerprint(ip, 80)
            elif 443 in port_numbers:
                http_info = self.http_fingerprint(ip, 443)
            elif 8080 in port_numbers:
                http_info = self.http_fingerprint(ip, 8080)

            # Identificar OS
            os_info, confidence, detection_method = self.identify_os(ttl, open_ports, hostname)

            # Determinar tipo de dispositivo
            device_type = self.get_device_type(open_ports, hostname, os_info, http_info)

            result = {
                'ip': str(ip),
                'hostname': hostname or 'N/A',
                'ttl': ttl,
                'os': os_info,
                'confidence': confidence,
                'detection_method': detection_method,
                'device_type': device_type,
                'open_ports': open_ports,
                'http_info': http_info,
                'scan_time': datetime.now().isoformat()
            }
            
            with self.lock:
                self.results.append(result)
                # Truncar strings longas para caber na tabela
                hostname_short = (hostname or 'N/A')[:20]
                os_short = os_info[:15]
                method_short = detection_method[:12]
                device_short = device_type[:35]

                print(f"✅ {str(ip):<15} | {hostname_short:<20} | {os_short:<15} | {method_short:<12} | {device_short:<35}")

            return result
            
        except Exception as e:
            print(f"❌ Erro ao escanear {str(ip)}: {e}")
            return None

    def scan_network(self):
        """Escaneia toda a rede"""
        self.print_banner()
        
        print(f"{'IP':<15} | {'Hostname':<20} | {'OS':<15} | {'Método':<12} | {'Dispositivo/Modelo':<35}")
        print("-" * 120)
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submeter todas as tarefas
            futures = {executor.submit(self.scan_host, ip): ip for ip in self.network.hosts()}
            
            # Processar resultados conforme completam
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    ip = futures[future]
                    print(f"❌ Erro ao processar {str(ip)}: {e}")
        
        end_time = time.time()
        
        # Resumo final
        self.print_summary(end_time - start_time)

    def print_summary(self, scan_time):
        """Exibe resumo do scan"""
        print("\n" + "=" * 120)
        print("📊 RESUMO DO SCAN AVANÇADO")
        print("=" * 120)
        print(f"⏱️  Tempo total: {scan_time:.2f} segundos")
        print(f"🎯 Hosts encontrados: {len(self.results)}")
        print(f"📡 Total de IPs escaneados: {self.network.num_addresses}")
        
        # Estatísticas por OS, dispositivos e métodos de detecção
        os_stats = {}
        device_stats = {}
        method_stats = {}

        for result in self.results:
            os_name = result['os']
            device_type = result['device_type']
            detection_method = result['detection_method']

            os_stats[os_name] = os_stats.get(os_name, 0) + 1
            device_stats[device_type] = device_stats.get(device_type, 0) + 1
            method_stats[detection_method] = method_stats.get(detection_method, 0) + 1

        print("\n🖥️  Sistemas Operacionais encontrados:")
        for os_name, count in sorted(os_stats.items()):
            print(f"   {os_name}: {count}")

        print("\n📱 Tipos de dispositivos encontrados:")
        for device_type, count in sorted(device_stats.items()):
            print(f"   {device_type}: {count}")

        print("\n🔍 Métodos de detecção utilizados:")
        for method, count in sorted(method_stats.items()):
            print(f"   {method}: {count}")

    def save_results(self, filename=None):
        """Salva resultados em arquivo JSON"""
        if filename is None:
            filename = f"network_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'scan_info': {
                    'network': str(self.network),
                    'scan_time': datetime.now().isoformat(),
                    'total_hosts': len(self.results)
                },
                'results': self.results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Resultados salvos em: {filename}")

def main():
    parser = argparse.ArgumentParser(description='Network Scanner - Identifica dispositivos na rede')
    parser.add_argument('--network', '-n', default='***********/16',
                       help='Rede para escanear (padrão: ***********/16)')
    parser.add_argument('--threads', '-t', type=int, default=50,
                       help='Número de threads (padrão: 50)')
    parser.add_argument('--timeout', type=int, default=2,
                       help='Timeout em segundos (padrão: 2)')
    parser.add_argument('--save', '-s', action='store_true',
                       help='Salvar resultados em arquivo JSON')
    
    args = parser.parse_args()

    # Aviso para redes grandes
    try:
        import ipaddress
        network_obj = ipaddress.IPv4Network(args.network)
        if network_obj.num_addresses > 1000:
            print(f"⚠️  AVISO: Scan de {network_obj.num_addresses} IPs pode demorar muito tempo!")
            print(f"📡 Rede: {args.network}")
            print(f"⚡ Threads: {args.threads}")
            print(f"⏱️  Tempo estimado: {network_obj.num_addresses // args.threads // 10} - {network_obj.num_addresses // args.threads // 5} minutos")
            confirm = input("Deseja continuar? (s/N): ")
            if not confirm.lower().startswith('s'):
                print("Scan cancelado.")
                return
    except:
        pass

    try:
        scanner = NetworkScanner(
            network=args.network,
            max_workers=args.threads,
            timeout=args.timeout
        )
        
        scanner.scan_network()
        
        if args.save:
            scanner.save_results()
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Scan interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro durante o scan: {e}")

if __name__ == "__main__":
    main()
